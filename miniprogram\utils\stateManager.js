// 状态管理器 - 重构版，专注于状态管理
import { STORAGE_KEYS } from '../constants/index.js'
import debounceManager from './debounceManager.js'
import { getConfigManager } from './configManager.js'
import AuthStatusCalculator from './authStatusCalculator.js'
import SecureLogger from './secureLogger.js'

class StateManager {
  constructor() {
    this.state = {
      isLogin: false,
      hasBindPhone: false,
      isHouseAuth: false,
      authStatus: 'none', // 认证状态：none, pending, verified
      userInfo: null,
      ownerInfo: null,
      communityInfo: null,
      houseInfo: null,
      tokenUser: null,
      userType: '1', // 用户类型：1=业主, 2=物业
      isPropertyUser: false, // 是否为物业用户
      needsRefresh: false // 全局刷新标记
    }
    this.configManager = getConfigManager()
    this.loadFromStorage()
  }

  // 从本地存储加载状态
  loadFromStorage() {
    try {
      const storageData = {
        token: wx.getStorageSync(STORAGE_KEYS.TOKEN),
        userInfo: wx.getStorageSync(STORAGE_KEYS.WX_USER_INFO),
        ownerInfo: wx.getStorageSync(STORAGE_KEYS.OWNER_INFO),
        communityInfo: wx.getStorageSync(STORAGE_KEYS.COMMUNITY_INFO),
        houseInfo: wx.getStorageSync(STORAGE_KEYS.HOUSE_INFO),
        tokenUser: wx.getStorageSync(STORAGE_KEYS.TOKEN_USER),
        userType: wx.getStorageSync('userType') || '1',
        isPropertyUser: wx.getStorageSync('isPropertyUser') || false
      }

      // 初始化配置管理器
      this.configManager.init(storageData.communityInfo)

      // 使用认证状态计算器计算状态
      const calculatedState = AuthStatusCalculator.calculateInitialState(storageData)

      this.state = {
        ...this.state,
        ...calculatedState,
        userInfo: storageData.userInfo,
        ownerInfo: storageData.ownerInfo,
        communityInfo: storageData.communityInfo,
        houseInfo: storageData.houseInfo,
        tokenUser: storageData.tokenUser,
        userType: storageData.userType,
        isPropertyUser: storageData.isPropertyUser
      }

      SecureLogger.log('StateManager', '状态加载完成', {
        hasToken: !!storageData.token,
        hasUserInfo: !!storageData.userInfo,
        authStatus: calculatedState.authStatus
      })
    } catch (error) {
      SecureLogger.error('StateManager', '加载状态失败', error)
    }
  }

  // 注意：parseExtJson和safeGetString方法已移至ConfigParser类

  // 更新状态（优化版本）
  setState(newState, options = {}) {
    const { immediate = false, skipSync = false } = options

    // 如果更新了communityInfo，更新配置管理器
    if (newState.communityInfo) {
      const hasConfigChanged = this.configManager.update(newState.communityInfo)
      if (hasConfigChanged) {
        // 配置更新了，重新设置截屏控制
        this.updateScreenshotControl()
      }
    }

    this.state = { ...this.state, ...newState }

    if (!skipSync) {
      this.syncToStorage(immediate)
      this.syncToGlobalData(immediate)
    }
  }

  // 检查配置是否需要更新
  checkConfigUpdate(serverCommunityInfo) {
    if (!serverCommunityInfo || !this.state.communityInfo) {
      return false
    }

    const localUpdateTime = this.state.communityInfo.updateTime
    const serverUpdateTime = serverCommunityInfo.updateTime

    // 如果服务器的更新时间比本地的新，说明配置有更新
    if (serverUpdateTime && localUpdateTime && serverUpdateTime !== localUpdateTime) {
      SecureLogger.log('StateManager', '检测到配置更新', {
        localTime: localUpdateTime,
        serverTime: serverUpdateTime
      })
      return true
    }

    return false
  }

  // 刷新配置信息
  async refreshConfig() {
    try {
      SecureLogger.log('StateManager', '开始刷新配置')

      const app = getApp()
      const res = await app.request({
        url: '/api/wx/index/status',
        method: 'POST',
        data: {}
      })

      if (res.code === 0 && res.data && res.data.communityInfo) {
        // 更新社区信息和配置
        this.setState({
          communityInfo: res.data.communityInfo
        })

        SecureLogger.log('StateManager', '配置刷新成功')
        return true
      } else {
        SecureLogger.error('StateManager', '配置刷新失败', res.msg)
        return false
      }
    } catch (error) {
      SecureLogger.error('StateManager', '配置刷新异常', error)
      return false
    }
  }

  // 同步到本地存储（防抖优化）
  syncToStorage(immediate = false) {
    const syncData = {
      userInfo: this.state.userInfo,
      ownerInfo: this.state.ownerInfo,
      communityInfo: this.state.communityInfo,
      houseInfo: this.state.houseInfo,
      tokenUser: this.state.tokenUser
    }

    const executor = (data) => {
      try {
        if (data.userInfo) {
          wx.setStorageSync(STORAGE_KEYS.WX_USER_INFO, data.userInfo)
          wx.setStorageSync('um_userid', data.userInfo.userId)
          wx.setStorageSync('um_unid', data.userInfo.openId)
        }
        if (data.ownerInfo) {
          wx.setStorageSync(STORAGE_KEYS.OWNER_INFO, data.ownerInfo)
        }
        if (data.communityInfo) {
          wx.setStorageSync(STORAGE_KEYS.COMMUNITY_INFO, data.communityInfo)
        }
        if (data.houseInfo) {
          wx.setStorageSync(STORAGE_KEYS.HOUSE_INFO, data.houseInfo)
        }
        if (data.tokenUser) {
          wx.setStorageSync(STORAGE_KEYS.TOKEN_USER, data.tokenUser)
        }
        SecureLogger.log('StateManager', '存储同步完成')
      } catch (error) {
        SecureLogger.error('StateManager', '同步存储失败', error)
      }
    }

    if (immediate) {
      executor(syncData)
    } else {
      // 使用防抖批量更新，200ms内的多次调用会合并
      debounceManager.batchUpdate('storage-sync', syncData, executor, 200)
    }
  }

  // 同步到全局数据（防抖优化）
  syncToGlobalData(immediate = false) {
    const syncData = {
      isLogin: this.state.isLogin,
      userInfo: this.state.userInfo,
      tokenUser: this.state.tokenUser,
      communityInfo: this.state.communityInfo
    }

    const executor = (data) => {
      const app = getApp()
      if (app && app.globalData) {
        // 只同步必要的数据到 globalData，保持 globalData 简洁
        Object.assign(app.globalData, data)
        SecureLogger.log('StateManager', '全局数据同步完成')
      }
    }

    if (immediate) {
      executor(syncData)
    } else {
      // 使用防抖批量更新，100ms内的多次调用会合并
      debounceManager.batchUpdate('global-sync', syncData, executor, 100)
    }
  }

  // 获取状态
  getState() {
    return {
      ...this.state,
      config: this.configManager.getAll() // 通过配置管理器获取配置
    }
  }

  // 清除所有状态（智能清除策略）
  clearState(force = false, callback) {
    console.log('[StateManager] 清除所有状态', force ? '(强制)' : '(智能)')

    // 如果不是强制清除，先检查是否应该保留状态
    if (!force && this.shouldPreserveState()) {
      console.log('[StateManager] 检测到网络问题，暂时保留登录状态')
      return
    }

    this.state = {
      isLogin: false,
      hasBindPhone: false,
      isHouseAuth: false,
      authStatus: 'none',
      userInfo: null,
      ownerInfo: null,
      communityInfo: null,
      houseInfo: null,
      tokenUser: null,
      userType: '1',
      isPropertyUser: false
    }

    // 清除本地存储
    try {
      wx.removeStorageSync(STORAGE_KEYS.TOKEN)
      wx.removeStorageSync(STORAGE_KEYS.WX_USER_INFO)
      wx.removeStorageSync(STORAGE_KEYS.OWNER_INFO)
      wx.removeStorageSync(STORAGE_KEYS.COMMUNITY_INFO)
      wx.removeStorageSync(STORAGE_KEYS.HOUSE_INFO)
      wx.removeStorageSync(STORAGE_KEYS.TOKEN_USER)
      wx.removeStorageSync('um_userid')
      wx.removeStorageSync('um_unid')
      wx.removeStorageSync('userType')
      wx.removeStorageSync('isPropertyUser')
      console.log('[StateManager] 本地存储清除完成')
    } catch (error) {
      console.error('[StateManager] 清除本地存储失败:', error)
    }

    this.syncToGlobalData()

    // 清理TabBar缓存
    this.clearTabBarCache()

    // 执行回调函数（如果提供）
    if (typeof callback === 'function') {
      callback()
    }
  }

  // 判断是否应该保留状态（智能判断）
  shouldPreserveState() {
    try {
      // 检查是否有有效的token
      const token = wx.getStorageSync(STORAGE_KEYS.TOKEN)
      if (!token) {
        console.log('[StateManager] 无token，不保留状态')
        return false
      }

      // 检查最近的网络错误记录
      const app = getApp()
      if (app && app.globalData && app.globalData.lastNetworkError) {
        const timeSinceError = Date.now() - app.globalData.lastNetworkError
        if (timeSinceError < 60000) { // 1分钟内有网络错误
          console.log('[StateManager] 检测到最近的网络错误，暂时保留登录状态')
          return true
        }
      }

      // 检查用户信息完整性
      const userInfo = wx.getStorageSync(STORAGE_KEYS.WX_USER_INFO)
      if (!userInfo || !userInfo.userId) {
        console.log('[StateManager] 用户信息不完整，不保留状态')
        return false
      }

      // 默认不保留状态，让token验证决定
      console.log('[StateManager] 默认不保留状态，等待token验证')
      return false
    } catch (error) {
      console.error('[StateManager] 检查状态保留条件失败，不保留状态:', error)
      return false
    }
  }

  // 清理TabBar缓存
  clearTabBarCache() {
    try {
      console.log('[StateManager] 清理TabBar缓存')

      // 获取当前页面的TabBar实例
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (typeof currentPage.getTabBar === 'function' && currentPage.getTabBar()) {
          const tabBar = currentPage.getTabBar()
          if (typeof tabBar.clearCache === 'function') {
            tabBar.clearCache()
            console.log('[StateManager] TabBar缓存已清理')
          }
        }
      }
    } catch (error) {
      console.error('[StateManager] 清理TabBar缓存失败:', error)
    }
  }

  // 设置登录成功状态（优化版）
  setLoginSuccess(loginData) {
    console.log('[StateManager] 设置登录成功状态:', loginData)

    // 批量保存关键数据，减少存储操作
    const storageUpdates = {}

    // 保存 token（只有房屋认证成功才有token）
    if (loginData.token) {
      storageUpdates[STORAGE_KEYS.TOKEN] = loginData.token
      console.log('[StateManager] 保存token')
    } else {
      console.log('[StateManager] 无token，跳过token保存')
    }

    // 立即保存关键用户信息
    if (loginData.userInfo) {
      storageUpdates[STORAGE_KEYS.WX_USER_INFO] = loginData.userInfo
      storageUpdates['um_userid'] = loginData.userInfo.userId
      storageUpdates['um_unid'] = loginData.userInfo.openId
    }

    // 保存其他信息
    if (loginData.ownerInfo) {
      storageUpdates[STORAGE_KEYS.OWNER_INFO] = loginData.ownerInfo
    }
    if (loginData.communityInfo) {
      storageUpdates[STORAGE_KEYS.COMMUNITY_INFO] = loginData.communityInfo
    }
    if (loginData.houseInfo) {
      storageUpdates[STORAGE_KEYS.HOUSE_INFO] = loginData.houseInfo
    }
    if (loginData.tokenUser) {
      storageUpdates[STORAGE_KEYS.TOKEN_USER] = loginData.tokenUser
    }

    // 保存用户类型
    if (loginData.userType) {
      storageUpdates['userType'] = loginData.userType
    }

    // 保存物业用户标识
    if (loginData.isPropertyUser !== undefined) {
      storageUpdates['isPropertyUser'] = loginData.isPropertyUser
    }

    // 批量写入存储
    try {
      Object.keys(storageUpdates).forEach(key => {
        wx.setStorageSync(key, storageUpdates[key])
      })
      console.log('[StateManager] 批量存储完成')
    } catch (error) {
      console.error('[StateManager] 批量存储失败:', error)
    }

    // 计算认证状态
    const authStatus = this.calculateAuthStatus(loginData)

    // 更新状态（使用立即同步，避免防抖延迟）
    this.setState({
      isLogin: true,
      hasBindPhone: loginData.hasBindPhone || false,
      isHouseAuth: loginData.isHouseAuth || false,
      authStatus: authStatus,
      userInfo: loginData.userInfo,
      ownerInfo: loginData.ownerInfo,
      communityInfo: loginData.communityInfo,
      houseInfo: loginData.houseInfo,
      tokenUser: loginData.tokenUser,
      userType: loginData.userType || '1',
      isPropertyUser: loginData.isPropertyUser || false
    }, { immediate: true, skipSync: true })  // 跳过同步，因为已经手动保存
  }

  // 更新登录状态（用于手机号绑定后等场景）
  updateLoginState(updateData) {
    console.log('[StateManager] 更新登录状态:', updateData)

    // 批量保存更新的数据
    const storageUpdates = {}

    // 更新 token
    if (updateData.token) {
      storageUpdates[STORAGE_KEYS.TOKEN] = updateData.token
    }

    // 更新用户信息
    if (updateData.userInfo) {
      storageUpdates[STORAGE_KEYS.WX_USER_INFO] = updateData.userInfo
      storageUpdates['um_userid'] = updateData.userInfo.userId
      storageUpdates['um_unid'] = updateData.userInfo.openId
    }

    // 更新其他信息
    if (updateData.ownerInfo) {
      storageUpdates[STORAGE_KEYS.OWNER_INFO] = updateData.ownerInfo
    }
    if (updateData.communityInfo) {
      storageUpdates[STORAGE_KEYS.COMMUNITY_INFO] = updateData.communityInfo
    }
    if (updateData.houseInfo) {
      storageUpdates[STORAGE_KEYS.HOUSE_INFO] = updateData.houseInfo
    }
    if (updateData.tokenUser) {
      storageUpdates[STORAGE_KEYS.TOKEN_USER] = updateData.tokenUser
    }

    // 批量写入存储
    try {
      Object.keys(storageUpdates).forEach(key => {
        wx.setStorageSync(key, storageUpdates[key])
      })
      console.log('[StateManager] 状态更新存储完成')
    } catch (error) {
      console.error('[StateManager] 状态更新存储失败:', error)
    }

    // 合并状态并重新计算认证状态
    const mergedData = { ...this.state, ...updateData }
    const authStatus = this.calculateAuthStatus(mergedData)

    // 更新状态（使用立即同步）
    this.setState({ ...mergedData, authStatus }, { immediate: true, skipSync: true })
  }

  // 检查登录状态
  isLoggedIn() {
    return AuthStatusCalculator.isLoggedIn(this.state)
  }

  // 检查房屋认证状态
  isHouseAuthenticated() {
    return AuthStatusCalculator.isHouseAuthenticated(this.state)
  }

  // 计算认证状态（委托给AuthStatusCalculator）
  calculateAuthStatus(loginData) {
    return AuthStatusCalculator.calculateAuthStatus(loginData)
  }

  // 设置全局刷新标记
  setNeedsRefresh(needsRefresh = true) {
    this.state.needsRefresh = needsRefresh
    console.log('[StateManager] 设置全局刷新标记:', needsRefresh)
  }

  // 检查并清除刷新标记
  checkAndClearRefresh() {
    const needsRefresh = this.state.needsRefresh
    if (needsRefresh) {
      this.state.needsRefresh = false
      console.log('[StateManager] 检查到刷新标记，已清除')
    }
    return needsRefresh
  }

  // 清除所有状态和缓存（保留系统配置）
  clearAllState() {
    try {
      console.log('[StateManager] 开始清除所有用户状态')

      // 清除内存中的用户状态
      this.state = {
        userInfo: null,
        ownerInfo: null,
        communityInfo: null,
        houseInfo: null,
        isAuthenticated: false,
        lastRefreshTime: 0,
        needsRefresh: false
      }

      // 清除用户相关的本地存储键（不包括系统配置）
      const keysToRemove = [
        STORAGE_KEYS.TOKEN,
        STORAGE_KEYS.WX_USER_INFO,
        STORAGE_KEYS.OWNER_INFO,
        STORAGE_KEYS.COMMUNITY_INFO,
        STORAGE_KEYS.HOUSE_INFO,
        STORAGE_KEYS.LAST_REFRESH_TIME,
        'um_userid',
        'um_unid',
        'refresh_flag',
        'logs' // 清除日志
      ]

      keysToRemove.forEach(key => {
        try {
          wx.removeStorageSync(key)
          console.log(`[StateManager] 已清除存储键: ${key}`)
        } catch (e) {
          console.warn(`[StateManager] 清除存储键 ${key} 失败:`, e)
        }
      })

      console.log('[StateManager] 所有用户状态已清除，系统配置已保留')
    } catch (error) {
      console.error('[StateManager] 清除状态失败:', error)
      throw error
    }
  }

  // 更新截屏控制设置
  updateScreenshotControl() {
    try {
      const enableScreenshot = this.configManager.get('enable_screenshot', '1')

      if (enableScreenshot === '0') {
        // 禁止截屏
        if (wx.setVisualEffectOnCapture) {
          wx.setVisualEffectOnCapture({
            visualEffect: 'hidden',
            success: () => {
              console.log('[StateManager] 截屏控制已更新：禁止截屏')
            },
            fail: (err) => {
              console.warn('[StateManager] 更新截屏控制失败:', err)
            }
          })
        }
      } else {
        // 允许截屏，清除之前的设置
        if (wx.setVisualEffectOnCapture) {
          wx.setVisualEffectOnCapture({
            visualEffect: 'none',
            success: () => {
              console.log('[StateManager] 截屏控制已更新：允许截屏')
            },
            fail: (err) => {
              console.warn('[StateManager] 更新截屏控制失败:', err)
            }
          })
        }
      }
    } catch (error) {
      console.error('[StateManager] 更新截屏控制失败:', error)
    }
  }
}

// 创建单例
let stateManager = null

export function getStateManager() {
  if (!stateManager) {
    stateManager = new StateManager()
  }
  return stateManager
}

export default getStateManager 